package impl_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	affectedTestTables = []string{
		"fetched_assets",
	}

	conf                   *config.Config
	dbResourceProviderPool *test.DbResourceProviderPool
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown, dbProviderResourcePoolCleanup func()

	conf, _, teardown = test.InitTestServer()
	dbResourceProviderPool, dbProviderResourcePoolCleanup = test.NewDbResourceProviderPool(conf.PgDbConfigMap.GetOwnershipToDbConfigMap(), affectedTestTables, 2)

	// Intentionally removing epifi_test DB as we don't use it for secured loans dao, but only in PL service for auth
	delete(conf.DbConfigMap, "EPIFI_TECH")

	exitCode := m.Run()
	teardown()
	dbProviderResourcePoolCleanup()

	os.Exit(exitCode)
}
